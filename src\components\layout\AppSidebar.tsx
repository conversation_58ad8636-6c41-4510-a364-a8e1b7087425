import { NavLink, useLocation } from "react-router-dom";
import {
  Calendar,
  Users,
  LayoutDashboard,
  Heart,
  LogOut,
  User,
  Shield
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { useToast } from "@/hooks/use-toast";


const getNavigation = (t: (key: string) => string, isAdmin: boolean) => {
  if (isAdmin) {
    // Admin users only see the Administration tab
    return [
      { name: "Administration", href: "/admin", icon: Shield },
    ];
  }

  // Regular users see all other tabs
  return [
    { name: t('nav.dashboard'), href: "/", icon: LayoutDashboard },
    { name: t('nav.patients'), href: "/patients", icon: Users },
    { name: t('nav.appointments'), href: "/appointments", icon: Calendar },
    { name: t('nav.profile'), href: "/profile", icon: User },
  ];
};

export function AppSidebar() {
  const { state } = useSidebar();
  const location = useLocation();
  const currentPath = location.pathname;
  const isCollapsed = state === "collapsed";
  const { profile, signOut } = useAuth();
  const { t } = useLanguage();
  const { toast } = useToast();
  const navigation = getNavigation(t, profile?.role === "admin");

  const isActive = (path: string) => {
    if (path === "/") return currentPath === "/";
    return currentPath.startsWith(path);
  };

  const getNavClassName = (path: string) => {
    return isActive(path)
      ? "bg-primary text-primary-foreground font-medium"
      : "hover:bg-accent hover:text-accent-foreground";
  };

  const handleSignOut = async () => {
    try {
      toast({
        title: "Déconnexion en cours...",
        description: "Veuillez patienter.",
      });

      await signOut();

      // The signOut function will handle the redirect, so this toast might not be seen
      toast({
        title: "Succès",
        description: "Vous avez été déconnecté avec succès.",
      });
    } catch (error) {
      console.error('Sign out error:', error);
      toast({
        title: "Erreur",
        description: "Échec de la déconnexion. Actualisation de la page...",
        variant: "destructive",
      });

      // Force reload as fallback
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  };

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader className="p-4 border-b">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
            <Heart className="w-5 h-5 text-white" />
          </div>
          {!isCollapsed && (
            <div>
              <h2 className="text-lg font-bold text-foreground">ClinicLine</h2>
              <Badge variant="secondary" className="text-xs">
                Professional
              </Badge>
            </div>
          )}
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Main Menu</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigation.map((item) => (
                <SidebarMenuItem key={item.name}>
                  <SidebarMenuButton asChild>
                    <NavLink
                      to={item.href}
                      className={`${getNavClassName(item.href)} transition-colors`}
                    >
                      <item.icon className={`${isCollapsed ? "w-5 h-5" : "w-4 h-4 mr-3"}`} />
                      {!isCollapsed && <span>{item.name}</span>}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>


      </SidebarContent>

      <SidebarFooter className="p-4 border-t">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSignOut}
          className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <LogOut className={`${isCollapsed ? "w-5 h-5" : "w-4 h-4 mr-3"}`} />
          {!isCollapsed && <span>{t('header.signOut')}</span>}
        </Button>
      </SidebarFooter>
    </Sidebar>
  );
}