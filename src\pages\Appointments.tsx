import { useState, useEffect, useCallback } from "react";
import { Calendar, Clock, Plus, ChevronLeft, ChevronRight, MoreVertical, Edit, Trash2, Eye, User, FileText, CalendarDays } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";
import { useLanguage } from "@/contexts/LanguageContext";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AddAppointmentForm } from "@/components/forms/AddAppointmentForm";
import { useToast } from "@/hooks/use-toast";

interface Appointment {
  id: number;
  appointment_date: string;
  appointment_time: string;
  status: "confirmed" | "completed" | "cancelled";
  notes?: string;
  patient_id: number;
  patients: {
    first_name: string;
    last_name: string;
  };
}

export default function Appointments() {
  const { t } = useLanguage();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [appointmentDialogOpen, setAppointmentDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState<{date: Date, time: string} | null>(null);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [appointmentToDelete, setAppointmentToDelete] = useState<Appointment | null>(null);
  const [renderKey, setRenderKey] = useState(0); // Force re-render key
  const { toast } = useToast();

  useEffect(() => {
    fetchAppointments();
  }, [currentDate]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);
      const targetDate = format(currentDate, 'yyyy-MM-dd');


      const { data, error } = await supabase
        .from('appointments')
        .select(`
          id,
          appointment_date,
          appointment_time,
          status,
          notes,
          patient_id,
          patients!inner (
            first_name,
            last_name
          )
        `)
        .eq('appointment_date', targetDate)
        .neq('status', 'cancelled')
        .order('appointment_time');

      if (error) {
        console.error('Error fetching appointments:', error);
        toast({
          title: "Error",
          description: "Failed to fetch appointments",
          variant: "destructive",
        });
      } else {
        const newAppointments = [...(data || [])]; // Force new array reference

        setAppointments(newAppointments);
        setRenderKey(prev => prev + 1);
      }
    } catch (error) {
      console.error('Error fetching appointments:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateAppointmentStatus = async (appointmentId: number, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('appointments')
        .update({ status: newStatus })
        .eq('id', appointmentId);

      if (error) {
        console.error('Error updating appointment status:', error);
        toast({
          title: "Error",
          description: "Failed to update appointment status",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: `Appointment marked as ${newStatus}`,
        });
        fetchAppointments();
      }
    } catch (error) {
      console.error('Error updating appointment status:', error);
    }
  };

  const handleViewAppointment = (appointment: Appointment) => {
    setSelectedAppointment(appointment);
    setViewDialogOpen(true);
  };

  const handleEditAppointment = (appointment: Appointment) => {
    setSelectedAppointment(appointment);
    setEditDialogOpen(true);
  };

  const handleDeleteAppointment = (appointment: Appointment) => {
    setAppointmentToDelete(appointment);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteAppointment = async () => {
    if (!appointmentToDelete) return;

    try {
      const { error } = await supabase
        .from('appointments')
        .delete()
        .eq('id', appointmentToDelete.id);

      if (error) {
        console.error('Error deleting appointment:', error);
        toast({
          title: "Error",
          description: "Failed to delete appointment",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: "Appointment deleted successfully",
        });
        fetchAppointments();
        setDeleteDialogOpen(false);
        setAppointmentToDelete(null);
      }
    } catch (error) {
      console.error('Error deleting appointment:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return "default";
      case "completed":
        return "secondary";
      case "cancelled":
        return "destructive";
      default:
        return "outline";
    }
  };

  const timeSlots = [
    "08:00", "08:30", "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
    "12:00", "12:30", "13:00", "13:30", "14:00", "14:30", "15:00", "15:30",
    "16:00", "16:30", "17:00", "17:30"
  ];

  const getAppointmentForSlot = useCallback((time: string) => {
    // Normalize time format - database stores "HH:MM:SS", slots use "HH:MM"
    const appointment = appointments.find(apt => {
      const dbTime = apt.appointment_time.substring(0, 5); // Extract "HH:MM" from "HH:MM:SS"
      return dbTime === time;
    });
    return appointment;
  }, [appointments]);

  const forceRefresh = useCallback(() => {
    setRenderKey(prev => prev + 1);
    fetchAppointments();
  }, []);

  const handleSlotClick = (time: string) => {
    const appointment = getAppointmentForSlot(time);
    if (!appointment) {
      setSelectedSlot({ date: currentDate, time });
      setAppointmentDialogOpen(true);
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const navigateDate = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate);
    newDate.setDate(currentDate.getDate() + (direction === "next" ? 1 : -1));
    setCurrentDate(newDate);
  };

  return (
    <div className="space-y-6 relative">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl p-6 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">Planificateur de Rendez-vous</h1>
            <p className="text-blue-100 text-lg">
              {t('dashboard.manageAppointments')} - {format(currentDate, 'EEEE dd MMMM yyyy', { locale: fr })}
            </p>
            <div className="flex items-center gap-4 mt-3">
              <div className="flex items-center gap-2 bg-white/20 rounded-full px-3 py-1">
                <div className="w-2 h-2 rounded-full bg-green-400"></div>
                <span className="text-sm">{t('dashboard.availableSlots')}: {timeSlots.filter(time => !getAppointmentForSlot(time)).length} {t('dashboard.slots')}</span>
              </div>
              <div className="flex items-center gap-2 bg-white/20 rounded-full px-3 py-1">
                <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                <span className="text-sm">{t('dashboard.bookedSlots')}: {appointments.length} {t('dashboard.appointments')}</span>
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-3">
            <Button
              className="bg-white text-blue-600 hover:bg-blue-50 shadow-lg font-semibold px-6 py-3 text-lg"
              onClick={() => setAppointmentDialogOpen(true)}
            >
              <Plus className="w-5 h-5 mr-2" />
              {t('dashboard.newAppointment')}
            </Button>
            <div className="text-center text-blue-100 text-sm">
              {t('dashboard.quickBookSlots')}
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Date Navigation */}
      <Card className="shadow-lg border-0 bg-gradient-to-r from-gray-50 to-white">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="lg"
                onClick={() => navigateDate("prev")}
                className="hover:bg-blue-50 hover:border-blue-300 transition-all duration-200"
              >
                <ChevronLeft className="w-5 h-5" />
                {t('ui.previousDay')}
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => navigateDate("next")}
                className="hover:bg-blue-50 hover:border-blue-300 transition-all duration-200"
              >
                {t('ui.nextDay')}
                <ChevronRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-800">{formatDate(currentDate)}</h2>
              <p className="text-sm text-gray-500 mt-1">
                {appointments.length} {t('dashboard.scheduledFor')}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentDate(new Date())}
                className="text-blue-600 border-blue-200 hover:bg-blue-50"
              >
                {t('common.today')}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Content Grid */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Available Time Slots - Left Column */}
        <div className="lg:col-span-1">
          <Card className="shadow-soft h-fit">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
              <CardTitle className="flex items-center gap-2 text-blue-800">
                <Clock className="w-5 h-5" />
                {t('appointments.availableSlots')}
              </CardTitle>
              <p className="text-sm text-blue-600">{t('appointments.clickToBook')}</p>
            </CardHeader>
            <CardContent className="p-4">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="text-muted-foreground mt-2">{t('appointments.loadingSlots')}</p>
                </div>
              ) : (
                <div key={`slots-${renderKey}`} className="space-y-2 max-h-80 overflow-y-auto">
                  {timeSlots.map((time) => {
                    const appointment = getAppointmentForSlot(time);
                    const isAvailable = !appointment;

                    return (
                      <div
                        key={`${time}-${appointments.length}-${renderKey}`}
                        className={cn(
                          "flex items-center justify-between p-3 rounded-xl border-2 transition-all duration-200",
                          isAvailable
                            ? "bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 hover:from-green-100 hover:to-emerald-100 hover:border-green-300 cursor-pointer hover:shadow-md"
                            : "bg-gradient-to-r from-red-50 to-pink-50 border-red-200 cursor-not-allowed opacity-75"
                        )}
                        onClick={isAvailable ? () => handleSlotClick(time) : undefined}
                      >
                        <div className="flex items-center gap-3">
                          <div className={cn(
                            "w-3 h-3 rounded-full",
                            isAvailable ? "bg-green-500 animate-pulse" : "bg-red-500"
                          )}></div>
                          <span className={cn(
                            "font-semibold",
                            isAvailable ? "text-green-800" : "text-red-800"
                          )}>{time}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {isAvailable ? (
                            <>
                              <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">{t('appointments.available')}</span>
                              <Plus className="w-4 h-4 text-green-600" />
                            </>
                          ) : (
                            <>
                              <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full font-medium">{t('appointments.unavailable')}</span>
                              <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
                                <div className="w-2 h-0.5 bg-white"></div>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Today's Appointments - Right Column */}
        <div className="lg:col-span-2">
          <Card className="shadow-soft">
            <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-t-lg">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2 text-purple-800">
                    <CalendarDays className="w-5 h-5" />
                    {t('appointments.todaysAppointments')} ({appointments.length})
                  </CardTitle>
                  <p className="text-sm text-purple-600 mt-1">{t('dashboard.manageScheduled')}</p>
                </div>
                <div className="flex gap-2">
                  <div className="flex items-center gap-1 text-xs">
                    <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                    <span className="text-yellow-700">{t('status.confirmed')} ({appointments.filter(a => a.status === 'confirmed').length})</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs">
                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                    <span className="text-green-700">{t('status.completed')} ({appointments.filter(a => a.status === 'completed').length})</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs">
                    <div className="w-2 h-2 rounded-full bg-red-500"></div>
                    <span className="text-red-700">{t('status.cancelled')} ({appointments.filter(a => a.status === 'cancelled').length})</span>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-4">
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="text-muted-foreground mt-2">Loading appointments...</p>
                </div>
              ) : appointments.length === 0 ? (
                <div className="text-center py-12">
                  <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">{t('appointments.noAppointmentsToday')}</h3>
                  <p className="text-gray-400 mb-4">{t('appointments.startBooking')}</p>
                  <Button
                    onClick={() => setAppointmentDialogOpen(true)}
                    className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {t('appointments.bookAppointment')}
                  </Button>
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {appointments.map((appointment) => (
                    <div
                      key={appointment.id}
                      className={cn(
                        "relative p-4 rounded-xl border-2 transition-all duration-300 shadow-sm hover:shadow-md",
                        appointment.status === 'completed'
                          ? "bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 hover:border-green-300"
                          : appointment.status === 'cancelled'
                          ? "bg-gradient-to-r from-red-50 to-pink-50 border-red-200 hover:border-red-300 opacity-75"
                          : "bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-200 hover:border-yellow-300"
                      )}
                    >
                      {/* Status Indicator */}
                      <div className={cn(
                        "absolute top-2 right-2 w-3 h-3 rounded-full",
                        appointment.status === 'completed' ? "bg-green-500" :
                        appointment.status === 'cancelled' ? "bg-red-500" : "bg-yellow-500"
                      )}></div>

                      <div className="flex items-start gap-4">
                        {/* Time Badge */}
                        <div className="flex-shrink-0">
                          <div className="bg-white rounded-lg p-3 border-2 border-gray-200 shadow-sm text-center min-w-[80px]">
                            <div className="text-lg font-bold text-gray-800">{appointment.appointment_time}</div>
                            <div className="text-xs text-gray-500">HEURE</div>
                          </div>
                        </div>

                        {/* Appointment Details */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <User className="w-4 h-4 text-gray-500" />
                                <h3 className="text-lg font-bold text-gray-800 truncate">
                                  {appointment.patients.first_name} {appointment.patients.last_name}
                                </h3>
                                <Badge
                                  variant={getStatusColor(appointment.status)}
                                  className={cn(
                                    "text-xs px-2 py-1 font-semibold",
                                    appointment.status === 'completed' && "bg-green-100 text-green-800 border-green-300",
                                    appointment.status === 'cancelled' && "bg-red-100 text-red-800 border-red-300",
                                    appointment.status === 'confirmed' && "bg-yellow-100 text-yellow-800 border-yellow-300"
                                  )}
                                >
                                  {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                                </Badge>
                              </div>

                              <div className="flex items-center gap-2 text-sm text-gray-600 mb-3">
                                <FileText className="w-4 h-4" />
                                <span className="truncate">{appointment.notes || 'No notes provided'}</span>
                              </div>

                              {/* Action Buttons */}
                              <div className="flex items-center gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleViewAppointment(appointment)}
                                  className="text-blue-600 border-blue-200 hover:bg-blue-50"
                                >
                                  <Eye className="w-3 h-3 mr-1" />
                                  View
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleEditAppointment(appointment)}
                                  className="text-green-600 border-green-200 hover:bg-green-50"
                                >
                                  <Edit className="w-3 h-3 mr-1" />
                                  Edit
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDeleteAppointment(appointment)}
                                  className="text-red-600 border-red-200 hover:bg-red-50"
                                >
                                  <Trash2 className="w-3 h-3 mr-1" />
                                  Delete
                                </Button>

                                {/* Status Change Dropdown */}
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button size="sm" variant="outline" className="px-2">
                                      <MoreVertical className="w-3 h-3" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end" className="w-48">
                                    <DropdownMenuItem
                                      onClick={() => updateAppointmentStatus(appointment.id, 'confirmed')}
                                      disabled={appointment.status === 'confirmed'}
                                      className="flex items-center gap-2"
                                    >
                                      <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                                      Marquer comme Confirmé
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() => updateAppointmentStatus(appointment.id, 'completed')}
                                      disabled={appointment.status === 'completed'}
                                      className="flex items-center gap-2"
                                    >
                                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                      Marquer comme Terminé
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() => updateAppointmentStatus(appointment.id, 'cancelled')}
                                      disabled={appointment.status === 'cancelled'}
                                      className="text-destructive flex items-center gap-2"
                                    >
                                      <div className="w-2 h-2 rounded-full bg-red-500"></div>
                                      Annuler Rendez-vous
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>



      {/* Add Appointment Dialog */}
      <Dialog open={appointmentDialogOpen} onOpenChange={setAppointmentDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              Ajouter Nouveau Rendez-vous
              {selectedSlot && (
                <span className="text-sm font-normal text-muted-foreground ml-2">
                  pour le {format(selectedSlot.date, 'dd MMM yyyy', { locale: fr })} à {selectedSlot.time}
                </span>
              )}
            </DialogTitle>
          </DialogHeader>
          <AddAppointmentForm
            onSuccess={() => {
              setAppointmentDialogOpen(false);
              setSelectedSlot(null);
              // Add small delay to ensure database is updated
              setTimeout(() => {
                forceRefresh();
              }, 100);
            }}
            initialData={selectedSlot ? {
              appointmentDate: selectedSlot.date,
              appointmentTime: selectedSlot.time
            } : undefined}
          />
        </DialogContent>
      </Dialog>

      {/* Enhanced View Appointment Dialog */}
      <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 -m-6 p-6 rounded-t-lg mb-6">
            <DialogTitle className="text-2xl text-blue-800 flex items-center gap-2">
              <Eye className="w-6 h-6" />
              Détails du Rendez-vous
            </DialogTitle>
          </DialogHeader>
          {selectedAppointment && (
            <div className="space-y-6">
              {/* Patient Info Card */}
              <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-200">
                <div className="flex items-center gap-3 mb-3">
                  <User className="w-5 h-5 text-purple-600" />
                  <h3 className="text-lg font-semibold text-purple-800">Patient Information</h3>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-purple-600">Full Name</label>
                    <p className="text-xl font-bold text-purple-900">
                      {selectedAppointment.patients.first_name} {selectedAppointment.patients.last_name}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-purple-600">Status</label>
                    <div className="mt-1">
                      <Badge
                        variant={getStatusColor(selectedAppointment.status)}
                        className={cn(
                          "text-sm px-3 py-1 font-semibold",
                          selectedAppointment.status === 'completed' && "bg-green-100 text-green-800 border-green-300",
                          selectedAppointment.status === 'cancelled' && "bg-red-100 text-red-800 border-red-300",
                          selectedAppointment.status === 'confirmed' && "bg-yellow-100 text-yellow-800 border-yellow-300"
                        )}
                      >
                        {selectedAppointment.status.charAt(0).toUpperCase() + selectedAppointment.status.slice(1)}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              {/* Appointment Details Card */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-xl border border-green-200">
                <div className="flex items-center gap-3 mb-3">
                  <CalendarDays className="w-5 h-5 text-green-600" />
                  <h3 className="text-lg font-semibold text-green-800">Détails du Rendez-vous</h3>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-green-600">Date</label>
                    <p className="text-xl font-bold text-green-900">
                      {format(new Date(selectedAppointment.appointment_date), 'EEEE dd MMMM yyyy', { locale: fr })}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-green-600">Heure</label>
                    <p className="text-xl font-bold text-green-900">{selectedAppointment.appointment_time}</p>
                  </div>
                </div>
              </div>

              {/* Notes Card */}
              <div className="bg-gradient-to-r from-orange-50 to-yellow-50 p-4 rounded-xl border border-orange-200">
                <div className="flex items-center gap-3 mb-3">
                  <FileText className="w-5 h-5 text-orange-600" />
                  <h3 className="text-lg font-semibold text-orange-800">Notes & Comments</h3>
                </div>
                <div className="bg-white p-4 rounded-lg border border-orange-100">
                  <p className="text-gray-800 leading-relaxed">
                    {selectedAppointment.notes || 'Aucune note ni commentaire na été fourni pour ce rendez-vous.'}
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => {
                    setViewDialogOpen(false);
                    handleEditAppointment(selectedAppointment);
                  }}
                  className="text-green-600 border-green-200 hover:bg-green-50"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Appointment
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setViewDialogOpen(false);
                    handleDeleteAppointment(selectedAppointment);
                  }}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete Appointment
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Appointment Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Appointment</DialogTitle>
          </DialogHeader>
          {selectedAppointment && (
            <AddAppointmentForm
              onSuccess={() => {
                setEditDialogOpen(false);
                setSelectedAppointment(null);
                setTimeout(() => {
                  forceRefresh();
                }, 100);
              }}
              initialData={{
                appointmentDate: new Date(selectedAppointment.appointment_date),
                appointmentTime: selectedAppointment.appointment_time,
                firstName: selectedAppointment.patients.first_name,
                lastName: selectedAppointment.patients.last_name,
                status: selectedAppointment.status,
                notes: selectedAppointment.notes || ''
              }}
              isEditing={true}
              appointmentId={selectedAppointment.id}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Appointment</AlertDialogTitle>
            <AlertDialogDescription>
              Êtes-vous sûr de vouloir supprimer ce rendez-vous ? Cette action ne peut pas être annulée.
              {appointmentToDelete && (
                <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                  <p><strong>Patient:</strong> {appointmentToDelete.patients.first_name} {appointmentToDelete.patients.last_name}</p>
                  <p><strong>Date:</strong> {format(new Date(appointmentToDelete.appointment_date), 'dd MMM yyyy', { locale: fr })}</p>
                  <p><strong>Heure:</strong> {appointmentToDelete.appointment_time}</p>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteAppointment}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Appointment
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

    </div>
  );
}
