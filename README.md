# 🏥 ClinicLine - Système de Gestion de Clinique

**ClinicLine** est une application web moderne de gestion de clinique médicale, développée avec React et Supabase. Elle offre une interface intuitive pour la gestion des patients, des rendez-vous, et l'administration des utilisateurs.

## ✨ Fonctionnalités

### 👥 **Gestion des Utilisateurs**

- **Authentification sécurisée** avec Supabase Auth
- **Rôles multiples** : Médecin, Infirmière, Administrateur
- **Réinitialisation de mot de passe** par email
- **Contrôle d'accès** basé sur les rôles

### 🏥 **Gestion des Patients**

- **Dossiers patients complets** avec informations médicales
- **Historique médical** et notes de consultation
- **Informations de contact** et données personnelles
- **Recherche et filtrage** avancés

### 📅 **Gestion des Rendez-vous**

- **Planification intelligente** des rendez-vous
- **Prévention des doubles réservations**
- **Calendrier interactif** avec vue mensuelle/hebdomadaire
- **Notifications et rappels**

### 🛡️ **Administration Avancée**

- **Tableau de bord administrateur** complet
- **Gestion des utilisateurs** avec contrôles radio
- **Activation/blocage des comptes** en temps réel
- **Gestion des statuts de paiement**
- **Statistiques et rapports**

### 🌐 **Interface Multilingue**

- **Support Français/Arabe** avec commutation facile
- **Interface responsive** pour tous les appareils
- **Design moderne** avec Tailwind CSS et shadcn/ui

## 🚀 Installation et Configuration

### **Prérequis**

- Node.js 18+ et npm
- Compte Supabase
- Git

### **Installation**

```bash
# Cloner le repository
git clone <YOUR_REPOSITORY_URL>
cd clinicline-flow-pilot

# Installer les dépendances
npm install

# Configurer les variables d'environnement
cp .env.example .env.local
# Éditer .env.local avec vos clés Supabase

# Démarrer le serveur de développement
npm run dev
```

### **Variables d'Environnement**

Créez un fichier `.env.local` avec :

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 🔧 Technologies Utilisées

### **Frontend**

- **React 18** - Bibliothèque UI moderne
- **TypeScript** - Typage statique pour plus de sécurité
- **Vite** - Build tool rapide et moderne
- **Tailwind CSS** - Framework CSS utilitaire
- **shadcn/ui** - Composants UI élégants
- **React Router** - Navigation côté client
- **React Hook Form** - Gestion des formulaires
- **Date-fns** - Manipulation des dates

### **Backend & Base de Données**

- **Supabase** - Backend-as-a-Service
- **PostgreSQL** - Base de données relationnelle
- **Row Level Security (RLS)** - Sécurité au niveau des lignes
- **Supabase Auth** - Authentification et autorisation
- **Real-time subscriptions** - Mises à jour en temps réel

### **Outils de Développement**

- **ESLint** - Linting du code
- **Prettier** - Formatage du code
- **TypeScript** - Vérification de types
- **Vite HMR** - Rechargement à chaud

## 📁 Structure du Projet

```
clinicline-flow-pilot/
├── src/
│   ├── components/          # Composants réutilisables
│   │   ├── ui/             # Composants UI de base
│   │   ├── layout/         # Composants de mise en page
│   │   └── admin/          # Composants d'administration
│   ├── contexts/           # Contextes React (Auth, Language)
│   ├── hooks/              # Hooks personnalisés
│   ├── integrations/       # Intégrations externes (Supabase)
│   ├── pages/              # Pages de l'application
│   └── lib/                # Utilitaires et helpers
├── public/                 # Assets statiques
└── docs/                   # Documentation
```

## 🔐 Configuration de Sécurité

### **Authentification**

- Authentification par email/mot de passe
- Réinitialisation sécurisée des mots de passe
- Sessions JWT avec rotation des tokens
- Protection contre les attaques par force brute

### **Autorisation**

- Contrôle d'accès basé sur les rôles (RBAC)
- Politiques RLS au niveau de la base de données
- Validation côté client et serveur
- Isolation des données par utilisateur

### **Sécurité des Données**

- Chiffrement des données sensibles
- Validation et sanitisation des entrées
- Protection CSRF et XSS
- Audit trail des actions administratives

## 📧 Configuration Email

Pour configurer les emails de réinitialisation de mot de passe :

1. **Supabase Dashboard** → Authentication → Settings
2. **Site URL** : `http://localhost:8080` (dev) / `https://yourdomain.com` (prod)
3. **Redirect URLs** : Ajouter `/reset-password` et `/auth`
4. **Email Templates** : Personnaliser le template français

Voir `SUPABASE_EMAIL_CONFIG.md` pour les détails complets.

## 🚀 Déploiement

### **Développement**

```bash
npm run dev          # Serveur de développement
npm run build        # Build de production
npm run preview      # Aperçu du build
```

### **Production**

1. **Build** : `npm run build`
2. **Deploy** : Déployer le dossier `dist/` sur votre hébergeur
3. **Variables** : Configurer les variables d'environnement de production
4. **Supabase** : Mettre à jour les URLs de redirection

### **Hébergeurs Recommandés**

- **Vercel** - Déploiement automatique depuis Git
- **Netlify** - Hébergement statique avec CI/CD
- **Firebase Hosting** - Hébergement Google
- **AWS S3 + CloudFront** - Solution AWS

## 🧪 Tests et Qualité

### **Tests**

```bash
npm run test         # Tests unitaires
npm run test:e2e     # Tests end-to-end
npm run coverage     # Couverture de code
```

### **Qualité du Code**

```bash
npm run lint         # Vérification ESLint
npm run format       # Formatage Prettier
npm run type-check   # Vérification TypeScript
```

## 📚 Documentation

- **`RESET_PASSWORD_FEATURE.md`** - Guide de la fonctionnalité de réinitialisation
- **`SUPABASE_EMAIL_CONFIG.md`** - Configuration des emails Supabase
- **`create-first-admin.md`** - Création du premier administrateur

## 🤝 Contribution

1. **Fork** le projet
2. **Créer** une branche feature (`git checkout -b feature/AmazingFeature`)
3. **Commit** vos changements (`git commit -m 'Add AmazingFeature'`)
4. **Push** vers la branche (`git push origin feature/AmazingFeature`)
5. **Ouvrir** une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

Pour obtenir de l'aide :

- **Issues GitHub** : Signaler des bugs ou demander des fonctionnalités
- **Documentation** : Consulter les guides dans `/docs`
- **Email** : Contacter l'équipe de développement

---

**Développé avec ❤️ pour la gestion moderne des cliniques médicales**
