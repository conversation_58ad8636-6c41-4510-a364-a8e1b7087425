import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface Profile {
  id: string;
  first_name: string;
  last_name: string;
  role: 'doctor' | 'nurse' | 'admin';
  created_at: string;
  active: boolean;
  payed: boolean;
}

interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: Profile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, firstName: string, lastName: string, role: 'doctor' | 'nurse' | 'admin') => Promise<{ error: any }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Utility function to clear all auth-related data
const clearAuthData = () => {
  // Clear all localStorage items that might contain auth data
  const keysToRemove = Object.keys(localStorage).filter(key =>
    key.startsWith('supabase') ||
    key.startsWith('sb-') ||
    key.includes('auth')
  );

  keysToRemove.forEach(key => {
    localStorage.removeItem(key);
  });

  // Also clear sessionStorage just in case
  const sessionKeysToRemove = Object.keys(sessionStorage).filter(key =>
    key.startsWith('supabase') ||
    key.startsWith('sb-') ||
    key.includes('auth')
  );

  sessionKeysToRemove.forEach(key => {
    sessionStorage.removeItem(key);
  });
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        setLoading(false);
        return;
      }

      // Check if user is blocked
      if (!data.active) {
        console.log('User account is blocked');
        await supabase.auth.signOut();
        clearAuthData();
        setLoading(false);
        return;
      }

      setProfile(data as Profile);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching profile:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('Auth state change:', event, session?.user?.id);

        setSession(session);
        setUser(session?.user ?? null);

        // Handle different auth events
        if (event === 'SIGNED_OUT' || !session) {
          // Clear all state on sign out
          setProfile(null);
          setUser(null);
          setSession(null);
          setLoading(false);
        } else if (session?.user) {
          // Fetch profile when user logs in
          setTimeout(() => {
            fetchProfile(session.user.id);
          }, 0);
        }

        setLoading(false);
      }
    );

    // Check for existing session
    supabase.auth.getSession().then(({ data: { session }, error }) => {
      if (error) {
        console.error('Error getting session:', error);
        clearAuthData();
      }

      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        setTimeout(() => {
          fetchProfile(session.user.id);
        }, 0);
      } else {
        setLoading(false);
      }
    }).catch((error) => {
      console.error('Session check failed:', error);
      clearAuthData();
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signUp = async (email: string, password: string, firstName: string, lastName: string, role: 'doctor' | 'nurse' | 'admin') => {
    const redirectUrl = `${window.location.origin}/`;
    
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl,
        data: {
          first_name: firstName,
          last_name: lastName,
          role: role
        }
      }
    });
    
    return { error };
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    return { error };
  };

  const signOut = async () => {
    try {
      console.log('Starting sign out process...');

      // Clear all local state first
      setProfile(null);
      setUser(null);
      setSession(null);

      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Supabase signOut error:', error);
      }

      // Force clear all auth data
      clearAuthData();

      console.log('Sign out completed, redirecting...');

      // Force reload the page to ensure clean state
      window.location.href = '/auth';

    } catch (error) {
      console.error('Error signing out:', error);
      // Even if there's an error, force clear everything and redirect
      setProfile(null);
      setUser(null);
      setSession(null);
      clearAuthData();
      window.location.href = '/auth';
    }
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      session, 
      profile, 
      loading, 
      signIn, 
      signUp, 
      signOut 
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};