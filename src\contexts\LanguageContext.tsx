import React, { createContext, useContext } from 'react';

interface LanguageContextType {
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface Translations {
  [key: string]: string;
}

const translations: Translations = {
  // Navigation
  'nav.dashboard': 'Tableau de Bord',
  'nav.patients': 'Patients',
  'nav.appointments': 'Rendez-vous',
  'nav.profile': 'Profil',

  // Auth
  'auth.login': 'Se connecter',
  'auth.logout': 'Se déconnecter',
  'auth.email': 'Email',
  'auth.password': 'Mot de passe',
  'auth.signup': "S'inscrire",
  'auth.firstName': 'Prénom',
  'auth.lastName': 'Nom',
  'auth.role': 'Rôle',
  'auth.doctor': 'Méde<PERSON>',
  'auth.nurse': 'Infirmière',
  
  // Patient form
  'patient.addNew': 'Ajouter un Nouveau Patient',
  'patient.information': 'Informations du Patient',
  'patient.firstName': 'Prénom',
  'patient.lastName': 'Nom',
  'patient.birthDate': 'Date de naissance',
  'patient.address': 'Adresse',
  'patient.phone': 'Numéro de téléphone',
  'patient.medicalHistory': 'Historique médical',
  'patient.allergies': 'Allergies',
  'patient.add': 'Ajouter le Patient',
  'patient.backToPatients': 'Retour aux Patients',
  
  // Appointment form
  'appointment.addNew': 'Programmer un Nouveau Rendez-vous',
  'appointment.details': 'Détails du Rendez-vous',
  'appointment.patientName': 'Nom du patient',
  'appointment.date': 'Date du rendez-vous',
  'appointment.time': 'Heure du rendez-vous',
  'appointment.status': 'Statut',
  'appointment.notes': 'Remarques',
  'appointment.confirmed': 'Confirmé',
  'appointment.completed': 'Terminé',
  'appointment.cancelled': 'Annulé',
  'appointment.schedule': 'Programmer le Rendez-vous',
  'appointment.backToAppointments': 'Retour aux Rendez-vous',

  // Common
  'common.save': 'Enregistrer',
  'common.cancel': 'Annuler',
  'common.delete': 'Supprimer',
  'common.edit': 'Modifier',
  'common.view': 'Voir',
  'common.reset': 'Réinitialiser',
  'common.loading': 'Chargement...',
  'common.error': 'Erreur',
  'common.success': 'Succès',
  'common.required': 'Obligatoire',
  'common.today': "Aujourd'hui",

  // Dashboard
  'dashboard.title': 'Tableau de Bord Médical',
  'dashboard.welcome': 'Bon retour',
  'dashboard.overview': 'Voici un aperçu de votre clinique pour',
  'dashboard.totalPatients': 'Total des Patients',
  'dashboard.todayAppointments': "Rendez-vous d'aujourd'hui",
  'dashboard.upcomingAppointments': 'Rendez-vous à venir',
  'dashboard.thisWeek': 'Cette semaine',
  'dashboard.todaySummary': "Résumé d'aujourd'hui",
  'dashboard.quickActions': 'Actions rapides',
  'dashboard.addPatient': 'Ajouter un patient',
  'dashboard.scheduleAppointment': 'Programmer un rendez-vous',
  'dashboard.vsLastPeriod': 'vs période précédente',
  'dashboard.manageAppointments': 'Gérez vos rendez-vous efficacement',
  'dashboard.availableSlots': 'Disponible',
  'dashboard.bookedSlots': 'Réservé',
  'dashboard.slots': 'créneaux',
  'dashboard.quickBookSlots': 'Réservation rapide des créneaux disponibles',
  'dashboard.scheduledFor': 'rendez-vous programmés',
  'dashboard.manageScheduled': 'Gérez vos rendez-vous programmés',
  'dashboard.newAppointment': 'Nouveau Rendez-vous',
  'dashboard.quickAppointmentBooking': 'Réservation rapide de rendez-vous',
  'dashboard.appointments': 'rendez-vous',
  'dashboard.patients': 'Patients',
  'dashboard.total': 'total',

  // Appointments Page
  'appointments.title': 'Rendez-vous',
  'appointments.availableSlots': 'Créneaux Disponibles',
  'appointments.clickToBook': 'Cliquez sur un créneau pour réserver',
  'appointments.todaysAppointments': "Rendez-vous d'Aujourd'hui",
  'appointments.noAppointments': "Aucun rendez-vous programmé aujourd'hui",
  'appointments.available': 'DISPONIBLE',
  'appointments.unavailable': 'INDISPONIBLE',
  'appointments.previousDay': 'Jour Précédent',
  'appointments.nextDay': 'Jour Suivant',
  'appointments.viewDetails': 'Voir Détails',
  'appointments.editAppointment': 'Modifier Rendez-vous',
  'appointments.deleteAppointment': 'Supprimer Rendez-vous',
  'appointments.noAppointmentsToday': "Aucun rendez-vous aujourd'hui",
  'appointments.startBooking': 'Commencez par réserver un rendez-vous parmi les créneaux disponibles',
  'appointments.bookAppointment': 'Réserver Rendez-vous',
  'appointments.loadingSlots': 'Chargement des créneaux disponibles...',
  'appointments.noNewNotifications': 'Aucune nouvelle notification',

  // Patients Page
  'patients.title': 'Patients',
  'patients.addNewPatient': 'Ajouter Nouveau Patient',
  'patients.searchPatients': 'Rechercher des patients...',
  'patients.name': 'Nom',
  'patients.age': 'Âge',
  'patients.phone': 'Téléphone',
  'patients.lastVisit': 'Dernière Visite',
  'patients.actions': 'Actions',
  'patients.noPatients': 'Aucun patient trouvé',
  'patients.totalPatients': 'Total des Patients',
  'patients.searchPlaceholder': 'Rechercher des patients...',
  'patients.noResults': 'Aucun patient trouvé correspondant à votre recherche',
  'patients.addNewPatientDesc': 'Ajouter un nouveau patient au système',
  'patients.patientDetails': 'Détails du Patient',
  'patients.contactInfo': 'Informations de Contact',
  'patients.medicalInfo': 'Informations Médicales',

  // Header
  'header.search': 'Rechercher...',
  'header.notifications': 'Notifications',
  'header.myAccount': 'Mon Compte',
  'header.profileSettings': 'Paramètres du Profil',
  'header.preferences': 'Préférences',
  'header.signOut': 'Se Déconnecter',

  // Forms
  'form.firstName': 'Prénom',
  'form.lastName': 'Nom de Famille',
  'form.email': 'Email',
  'form.phone': 'Numéro de Téléphone',
  'form.birthDate': 'Date de Naissance',
  'form.address': 'Adresse',
  'form.medicalHistory': 'Antécédents Médicaux',
  'form.allergies': 'Allergies',
  'form.medicament': 'Médicament',
  'form.profession': 'Profession',
  'form.codeCNSS': 'Code CNSS',
  'form.codeCNRPS': 'Code CNRPS',
  'form.appointmentDate': 'Date du Rendez-vous',
  'form.appointmentTime': 'Heure du Rendez-vous',
  'form.selectTime': "Sélectionner l'heure",
  'form.status': 'Statut',
  'form.notes': 'Notes',
  'form.optional': 'Optionnel',

  // Status
  'status.confirmed': 'Confirmé',
  'status.completed': 'Terminé',
  'status.cancelled': 'Annulé',
  'status.pending': 'En Attente',

  // Profile
  'profile.title': 'Mon Profil',
  'profile.fullName': 'Nom complet',
  'profile.email': 'Email',
  'profile.role': 'Rôle',
  'profile.settings': 'Paramètres',
  'profile.language': 'Langue',
  'profile.accountSettings': 'Gérez les paramètres et préférences de votre compte',

  // Form Validation
  'validation.enterFirstName': 'Veuillez saisir le prénom',
  'validation.enterLastName': 'Veuillez saisir le nom de famille',
  'validation.selectDate': 'Veuillez sélectionner une date',
  'validation.selectTime': 'Veuillez sélectionner une heure',

  // Dialog Actions
  'dialog.confirm': 'Confirmer',
  'dialog.close': 'Fermer',
  'dialog.continue': 'Continuer',

  // Additional UI Text
  'ui.doctor': 'Docteur',
  'ui.user': 'Utilisateur',
  'ui.nurse': 'Infirmière',
  'ui.admin': 'Administrateur',
  'ui.previousDay': 'Jour Précédent',
  'ui.nextDay': 'Jour Suivant',
  'ui.years': 'ans',
  'ui.year': 'an',

  // Time and Date
  'time.selectTime': "Sélectionner l'heure",
  'date.selectDate': 'Choisir une date',

  // Messages
  'message.patientAdded': 'Patient ajouté avec succès',
  'message.appointmentScheduled': 'Rendez-vous programmé avec succès',
  'message.loginSuccess': 'Connexion réussie',
  'message.logoutSuccess': 'Déconnexion réussie',
  'message.required': 'Ce champ est obligatoire',
  'message.doubleBooking': 'Ce créneau est déjà réservé',
  'message.appointmentDeleted': 'Rendez-vous supprimé avec succès',
  'message.appointmentUpdated': 'Rendez-vous mis à jour avec succès',
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const t = (key: string): string => {
    return translations[key] || key;
  };

  return (
    <LanguageContext.Provider value={{ t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
