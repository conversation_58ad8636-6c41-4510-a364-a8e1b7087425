// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://uapmttjmqmmtcvbpxcoc.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcG10dGptcW1tdGN2YnB4Y29jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNzc1ODcsImV4cCI6MjA2ODg1MzU4N30.qTMVVDxg48d4tvPhEPIT4NC6OoxooKDmSCCLOYKDLRA";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});